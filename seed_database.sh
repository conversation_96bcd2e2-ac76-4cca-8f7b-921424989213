#!/bin/bash

# Seed the database with task categories and test the createTask mutation

JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.0PqXkxiLsGtkEEw6bgqFXFo5F3Whw1xHrP_IHgUwWng"

GRAPHQL_URL="http://localhost:8080/api/dex-agent/graphql"

echo "=== Seeding Database with Task Categories ==="
echo ""

# First, let's manually insert the task categories using direct SQL
echo "1. Connecting to database to insert task categories..."

# Use the database connection from local.env
export PGPASSWORD=postgres
psql -h localhost -p 5433 -U postgres -d agent -c "
INSERT INTO task_categories (name, display_name, description, icon, sort_order, is_active) VALUES
('daily', 'Daily Tasks', 'Complete these tasks daily to earn points', 'calendar', 1, true),
('community', 'Community Tasks', 'Engage with our community to earn rewards', 'users', 2, true),
('trading', 'Trading Tasks', 'Complete trading activities to earn points', 'trending-up', 3, true)
ON CONFLICT (name) DO NOTHING;
"

echo ""
echo "2. Verifying task categories were created..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "query { taskCategories { id name displayName description icon sortOrder isActive } }"
  }' \
  "$GRAPHQL_URL" | jq '.'

echo ""
echo "3. Testing createTask mutation with valid category ID (1 = daily)..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { createTask(input: { categoryId: \"1\" name: \"Test Daily Task\" description: \"A test task for daily category\" taskType: DAILY frequency: DAILY points: 10 }) { id name description taskType frequency points sortOrder isActive category { id name displayName } } }"
  }' \
  "$GRAPHQL_URL" | jq '.'

echo ""
echo "4. Testing createTask mutation with sortOrder specified..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "X-Consumer-Username: xbit" \
  -d '{
    "query": "mutation { createTask(input: { categoryId: \"2\" name: \"Test Community Task\" description: \"A test task for community category\" taskType: ONE_TIME frequency: ONE_TIME points: 50 sortOrder: 5 }) { id name description taskType frequency points sortOrder isActive category { id name displayName } } }"
  }' \
  "$GRAPHQL_URL" | jq '.'

echo ""
echo "=== Database Seeding Complete ==="
